package com.adins.esign.businesslogic.impl.interfacing;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.model.custom.privy.PrivyRegisterIdentityBean;
import com.adins.esign.model.custom.privy.PrivyRegisterV2ResponseContainer;
import com.adins.esign.model.custom.privy.PrivyUploadDocOwnerBean;
import com.adins.esign.model.custom.privy.PrivyUploadDocRequest;
import com.adins.esign.model.custom.privy.PrivyUploadDocResponse;
import com.adins.esign.model.custom.privy.PrivyVerifV2CredentialsBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.util.PrivyUtils;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.privy.PrivyConfirmOtpRequest;
import com.adins.esign.webservices.model.privy.PrivyConfirmOtpResponse;
import com.adins.esign.webservices.model.privy.PrivyLivenessUrlResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterRequest;
import com.adins.esign.webservices.model.privy.PrivyRegisterResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterStatusRequest;
import com.adins.esign.webservices.model.privy.PrivyRegisterStatusResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterStatusV2Response;
import com.adins.esign.webservices.model.privy.PrivyRegisterV2Request;
import com.adins.esign.webservices.model.privy.PrivyRegisterV2Response;
import com.adins.esign.webservices.model.privy.PrivyRequestOtpRequest;
import com.adins.esign.webservices.model.privy.PrivyRequestOtpResponse;
import com.adins.esign.webservices.model.privy.PrivyUserAccessTokenRequest;
import com.adins.esign.webservices.model.privy.PrivyUserAccessTokenResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.PrivyException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.password.PasswordHash;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;

@Component
public class GenericPrivyLogic extends BaseLogic implements PrivyLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericPrivyLogic.class);
	
	@Value("${privy.register.uri}") private String registerUrl;
	@Value("${privy.registerstatus.uri}") private String registerStatusUrl;
	@Value("${privy.core.uri}") private String privyCoreUrl;
	@Value("${privy.useraccesstoken.uri}") private String privyUserAccessTokenUrl;
	@Value("${privy.requestotp.uri}") private String privyRequestOtp;
	@Value("${privy.uploaddoc.uri}") private String privyUploadDoc;
	@Value("${privy.confirmotp.uri}") private String privyConfirmOtp;
	
	// Privy Liveness
	@Value("${privy.liveness.base.uri}") private String livenessBaseUrl;
	@Value("${privy.liveness.uri}") private String livenessUrl;
	@Value("${privy.liveness.v3.uri}") private String livenessV3Url;

	// Privy Verification V2
	@Value("${privy.v2.verification.url}") private String verificationV2Url;

	
	@Autowired private Gson gson;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	
	private static final String FORM_DATA_PREFIX = "form-data; name=\"";
	private static final String FORM_DATA_SUFFIX = "\"";
	private static final int PRIVY_CERT_ACTIVE_DURATION_DAYS = 365;
	private static final String CONST_VERIFIED = "verified";
	
	// Register param
	private static final String KEY_PHONE = "phone";
	private static final String KEY_EMAIL = "email";
	private static final String KEY_SELFIE = "selfie";
	private static final String KEY_KTP = "ktp";
	private static final String KEY_IDENTITY = "identity";
	
	private static final String KEY_PRIVY_ID = "privyId";
	private static final String KEY_DOCUMENT_TITLE = "documentTitle";
	private static final String KEY_DOCTYPE = "docType";
	private static final String KEY_OWNER = "owner";
	private static final String KEY_RECIPIENTS = "recipients";
	private static final String KEY_DOCUMENT = "document";
	private static final String KEY_CODE = "code";
	private static final String KEY_TEMPLATE_ID = "templateId";
	
	// Privy register reject code
	public static final String REJECT_CODE_PRVS002 = "PRVS002";
	public static final String REJECT_CODE_PRVN002 = "PRVN002";
	public static final String REJECT_CODE_PRVN004 = "PRVN004";
	public static final String REJECT_CODE_PRVN005 = "PRVN005";
	public static final String REJECT_CODE_PRVN009 = "PRVN009";
	public static final String REJECT_CODE_PRVD005 = "PRVD005";
	public static final String REJECT_CODE_PRVD009 = "PRVD009";

	private static final String TRX_ID_PREFIX = "ADINS";

	private static final String LIVENESS_NOT_CONFIGURED_MSG = "businesslogic.privy.livenessnotconfigured";
	
	private Attachment prepareStringAttachment(String key, String value) {
		ContentDisposition cd = new ContentDisposition(FORM_DATA_PREFIX + key + FORM_DATA_SUFFIX);
		byte[] valueByteArray = value.getBytes();
		return new Attachment(key, new ByteArrayInputStream(valueByteArray), cd);
	}
	
	private Attachment prepareImageAttachment(String key, String base64Image, String filename) {
		ContentDisposition cd = new ContentDisposition(FORM_DATA_PREFIX + key + FORM_DATA_SUFFIX + "; filename=\"" + filename + "\"");
		byte[] fileByteArray = MssTool.imageStringToByteArray(base64Image);
		return new Attachment(key, new ByteArrayInputStream(fileByteArray), cd);
	}
	
	private Attachment prepareFileAttachment(String key, String base64File, String filename) {
		ContentDisposition cd = new ContentDisposition(FORM_DATA_PREFIX + key + FORM_DATA_SUFFIX + "; filename=\"" + filename + "\"");
		byte[] fileByteArray = Base64.getDecoder().decode(base64File);
		return new Attachment(key, new ByteArrayInputStream(fileByteArray), cd);
	}
	
	private void prepareRegisterRequest(List<Attachment> attachments, PrivyRegisterRequest request) {
		
		String selfieFilename = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + request.getPhone() + GlobalVal.FILE_FORMAT_JPEG;
		String ktpFilename = GlobalVal.PREFIX_PHOTO_ID_FILE_NAME + request.getPhone() + GlobalVal.FILE_FORMAT_JPEG;
		
		PrivyRegisterIdentityBean identity = new PrivyRegisterIdentityBean();
		identity.setNama(StringUtils.upperCase(request.getNama()));
		identity.setNik(request.getNik());
		identity.setTanggalLahir(request.getTanggalLahir());
		String identityJson = gson.toJson(identity);
		
		Map<String, Object> requestDetail = new HashMap<>();
		requestDetail.put(KEY_PHONE, request.getPhone());
		requestDetail.put(KEY_EMAIL, StringUtils.upperCase(request.getEmail()));
		requestDetail.put(KEY_SELFIE, selfieFilename);
		requestDetail.put(KEY_KTP, ktpFilename);
		requestDetail.put(KEY_IDENTITY, identityJson);
		LOG.info("Privy register request: {}", requestDetail);
		
		attachments.add(prepareStringAttachment(KEY_PHONE, request.getPhone()));
		attachments.add(prepareStringAttachment(KEY_EMAIL, StringUtils.upperCase(request.getEmail())));
		attachments.add(prepareImageAttachment(KEY_SELFIE, request.getSelfieImage(), selfieFilename));
		attachments.add(prepareImageAttachment(KEY_KTP, request.getKtpImage(), ktpFilename));
		attachments.add(prepareStringAttachment(KEY_IDENTITY, identityJson));
		
	}

	@Override
	public PrivyRegisterResponse register(PrivyRegisterRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		try {
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			String username = vot.getClientId();
			String password = vot.getClientSecret();
			String merchantKey = vot.getVendorMerchantKey();
			String authToken = HttpHeaders.buildBasicAuthorization(username, password);
			
			// Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORMDATA);
			mapHeader.add(HttpHeaders.PRIVY_MERCHANT_KEY, merchantKey);
			
			// Body
			List<Attachment> attachments = new ArrayList<>();
			prepareRegisterRequest(attachments, request);
			MultipartBody body = new MultipartBody(attachments);
			
			WebClient client = WebClient.create(privyCoreUrl + registerUrl).headers(mapHeader);
			Response response = client.post(body);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				String jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy register response: {}", jsonResponse);
				return gson.fromJson(jsonResponse, PrivyRegisterResponse.class);
			}
			
		} catch (Exception e) {
			LOG.error("Privy register exception: {}", e.getLocalizedMessage(), e);
			PrivyRegisterResponse response = new PrivyRegisterResponse();
			response.setCode(StatusCode.PRIVY_ERROR);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		
	}

	@Override
	public AmMsuser insertRegisteredUser(RegisterExternalRequest request, String privyId, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		String hashedPhone = MssTool.getHashedString(request.getTlp());
		String hashedIdNo = MssTool.getHashedString(request.getIdKtp());
		String emailService = (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) ? "1" :"0";
		String validationMessage = "";
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(request.getIdEmailHosting());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(request.getIdKtp());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = request.getNama().split(" ");
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setFullName(StringUtils.upperCase(request.getNama()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			
			String hashedPassword = "BFI".equals(tenant.getTenantCode()) ? "newInv2" : PasswordHash.createHash(request.getPassword());
			user.setPassword(hashedPassword);
			
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("0");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_PRIVY_ID);
		if (null == vendorUser) {
			byte[] phoneBytea = personalDataEncLogic.encryptFromString(request.getTlp());
			AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_PRIVY_CERTIFICATE_EXPIRE_TIME);
			validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1, new Object[] {"Duration Certificate PRIVY"}, audit);
			commonValidatorLogic.validateNotNull(gs, validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
			int certificateDurationVendor ;
			try {
				certificateDurationVendor = Integer.valueOf(gs.getGsValue());
			} catch (Exception e) {
				throw new CommonException("Invalid value Certificate Duration", ReasonCommon.INVALID_DATE_FORMAT);
			}
			Date activatedDate = new Date();
			Date expiredDate = DateUtils.addDays(activatedDate, certificateDurationVendor);
			
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_PRIVY_ID));
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(privyId);
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		}
		
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(request.getTglLahir(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(request.getSelfPhoto());
			byte[] idPhoto = MssTool.imageStringToByteArray(request.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(request.getProvinsi());
			zipcodeCityBean.setKota(request.getKota());
			zipcodeCityBean.setKecamatan(request.getKecamatan());
			zipcodeCityBean.setKelurahan(request.getKelurahan());
			zipcodeCityBean.setZipcode(request.getKodePos());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(request.getJenisKelamin()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(request.getTmpLahir()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(request.getIdKtp());
			personalDataBean.setPhoneRaw(request.getTlp());
			personalDataBean.setAddressRaw(StringUtils.upperCase(request.getAlamat()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
		
		return user;
	}

	@Override
	public String buildRegisterErrorMessage(PrivyRegisterResponse response, AuditContext audit) {
		if (CollectionUtils.isEmpty(response.getErrors())) {
			return StringUtils.EMPTY;
		}
		
		StringBuilder message = new StringBuilder();
		message.append("PSrE validations: ");
		for (int i = 0; i < response.getErrors().size(); i++) {
			String fieldErrorMessage = response.getErrors().get(i).getMessages().get(0);
			message.append(fieldErrorMessage);
			
			if (i != response.getErrors().size() - 1) {
				message.append(", ");
			}
		}
		return message.toString();
	}

	@Override
	public PrivyUserAccessTokenResponse userAccessToken(PrivyUserAccessTokenRequest request, MsTenant tenant,
			MsVendor vendor, AuditContext audit) {
		
		try {
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			String username = vot.getClientId();
			String password = vot.getClientSecret();
			String merchantKey = vot.getVendorMerchantKey();
			String authToken = HttpHeaders.buildBasicAuthorization(username, password);
			
			// Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORMDATA);
			mapHeader.add(HttpHeaders.PRIVY_MERCHANT_KEY, merchantKey);
			
			// Body
			List<Attachment> attachments = new ArrayList<>();
			Map<String, Object> requestDetail = new HashMap<>();
			requestDetail.put(KEY_PRIVY_ID, request.getPrivyId());
			LOG.info("Privy User Access Token request : {}", requestDetail);
			
			attachments.add(prepareStringAttachment(KEY_PRIVY_ID, request.getPrivyId()));
			MultipartBody body = new MultipartBody(attachments);
			
			WebClient client = WebClient.create(privyCoreUrl + privyUserAccessTokenUrl).headers(mapHeader);
			Response response = client.post(body);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				String jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy User Access Token response: {}", jsonResponse);
				return gson.fromJson(jsonResponse, PrivyUserAccessTokenResponse.class);
			}
			
		} catch (Exception e) {
			LOG.error("Privy User Access Token exception: {}", e.getLocalizedMessage(), e);
			PrivyUserAccessTokenResponse response = new PrivyUserAccessTokenResponse();
			response.setCode(StatusCode.PRIVY_ERROR);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		
	}

	@Override
	public PrivyRequestOtpResponse requestOtp(PrivyRequestOtpRequest request, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		
		try {
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			String username = vot.getClientId();
			String password = vot.getClientSecret();
			String merchantKey = vot.getVendorMerchantKey();
			String authToken = HttpHeaders.buildBasicAuthorization(username, password);
			
			// Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORMDATA);
			mapHeader.add(HttpHeaders.PRIVY_MERCHANT_KEY, merchantKey);
			mapHeader.add(HttpHeaders.PRIVY_TOKEN, request.getToken());
			
			WebClient client = WebClient.create(privyCoreUrl + privyRequestOtp).headers(mapHeader);
			Response response = client.post(null);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				String jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy Request OTP response: {}", jsonResponse);
				return gson.fromJson(jsonResponse, PrivyRequestOtpResponse.class);
			}
			
		} catch (Exception e) {
			LOG.error("Privy Request OTP exception: {}", e.getLocalizedMessage(), e);
			PrivyRequestOtpResponse response = new PrivyRequestOtpResponse();
			response.setCode(StatusCode.PRIVY_ERROR);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
	}

	@Override
	public PrivyRegisterStatusResponse checkRegisterStatus(MsTenant tenant, MsVendor vendor, String requestId, AuditContext audit) {
		
		try {
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			String username = vot.getClientId();
			String password = vot.getClientSecret();
			String merchantKey = vot.getVendorMerchantKey();
			String authToken = HttpHeaders.buildBasicAuthorization(username, password);
			
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			mapHeader.add(HttpHeaders.PRIVY_MERCHANT_KEY, merchantKey);
			
			WebClient client = WebClient.create(privyCoreUrl + registerStatusUrl).headers(mapHeader);
			
			PrivyRegisterStatusRequest request = new PrivyRegisterStatusRequest();
			request.setByField("request_id");
			request.setValue(requestId);
			String jsonRequest = gson.toJson(request);
			LOG.info("Privy register status request: {}", jsonRequest);
			
			Response response = client.post(jsonRequest);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				String jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy register status response: {}", jsonResponse);
				return gson.fromJson(jsonResponse, PrivyRegisterStatusResponse.class);
			}
			
		} catch (Exception e) {
			LOG.error("Privy register status exception: {}", e.getLocalizedMessage(), e);
			PrivyRegisterStatusResponse response = new PrivyRegisterStatusResponse();
			response.setCode(StatusCode.PRIVY_ERROR);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		
	}
	
	@Override
	public PrivyUploadDocResponse uploadDoc(PrivyUploadDocRequest request, MsTenant tenant, MsVendor vendor,
			AuditContext audit) throws IOException {
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		String username = vot.getClientId();
		String password = vot.getClientSecret();
		String merchantKey = vot.getVendorMerchantKey();
		String authToken = HttpHeaders.buildBasicAuthorization(username, password);
		
		// Header
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORMDATA);
		mapHeader.add("Merchant-Key", merchantKey);
		mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
		
		List<Attachment> attachments = new ArrayList<>();
		prepareUploadDocAttachment(request, attachments, vot);
		MultipartBody body = new MultipartBody(attachments);
		
		String url = privyCoreUrl + privyUploadDoc;
		
		WebClient client = WebClient.create(url).headers(mapHeader);
		Response response = client.post(body);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String jsonResponse = IOUtils.toString(isReader);
		LOG.info("Privy upload document response: {}", jsonResponse);
		
		return gson.fromJson(jsonResponse, PrivyUploadDocResponse.class);
	}
	
	private void prepareUploadDocAttachment(PrivyUploadDocRequest request, List<Attachment> attachments, MsVendoroftenant vot) {
		String enterprisePrivyId = vot.getVendorEnterpriseId();
		String enterpriseToken = vot.getVendorEnterpriseToken();
		
		PrivyUploadDocOwnerBean owner = new PrivyUploadDocOwnerBean();
		owner.setPrivyId(enterprisePrivyId);
		owner.setEnterpriseToken(enterpriseToken);
		
		String ownerString = gson.toJson(owner);
		request.setOwner(ownerString);
		
		String recepientsString = gson.toJson(request.getRecepients());
		String filename = request.getDocumentId() + GlobalVal.FILE_FORMAT_PDF;
		
		attachments.add(prepareStringAttachment(KEY_DOCUMENT_TITLE, request.getDocumentId()));
		attachments.add(prepareStringAttachment(KEY_DOCTYPE, request.getDocumentType()));
		attachments.add(prepareStringAttachment(KEY_OWNER, ownerString));
		attachments.add(prepareStringAttachment(KEY_RECIPIENTS, recepientsString));
		attachments.add(prepareFileAttachment(KEY_DOCUMENT, request.getDocumentFile(), filename));
		if (StringUtils.isNotBlank(request.getTemplateId())) {
			attachments.add(prepareStringAttachment(KEY_TEMPLATE_ID, request.getTemplateId()));
		}
		
		request.setDocumentFile("base64file");
		String requestString = gson.toJson(request);
		
		LOG.info("Request Upload Doc Privy : {}", requestString);
	}
	
	@Override
	public String buildUserAccessTokenErrorMessage(PrivyUserAccessTokenResponse response, AuditContext audit) {
		
		if (CollectionUtils.isEmpty(response.getErrors())) {
			return StringUtils.EMPTY;
		}
		
		StringBuilder message = new StringBuilder();
		for (int i = 0; i < response.getErrors().size(); i++) {
			String fieldName = response.getErrors().get(i).getField();
			String fieldErrorMessage = response.getErrors().get(i).getMessages().get(0);
			message.append(fieldName);
			message.append(": ");
			message.append(fieldErrorMessage);
			
			if (i != response.getErrors().size() - 1) {
				message.append(", ");
			}
		}
		return message.toString();
		
	}
	@Override
	public PrivyConfirmOtpResponse confirmOtp(PrivyConfirmOtpRequest request, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		try {
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			String username = vot.getClientId();
			String password = vot.getClientSecret();
			String merchantKey = vot.getVendorMerchantKey();
			String authToken = HttpHeaders.buildBasicAuthorization(username, password);

			
			// Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORMDATA);
			mapHeader.add(HttpHeaders.PRIVY_MERCHANT_KEY, merchantKey);
			mapHeader.add(HttpHeaders.PRIVY_TOKEN, request.getToken());
			
			// Body
			List<Attachment> attachments = new ArrayList<>();
			Map<String, Object> code = new HashMap<>();
			code.put(KEY_CODE, request.getCode());
			LOG.info("Privy confirm OTP request : {}", code);
			
			attachments.add(prepareStringAttachment(KEY_CODE, request.getCode()));
			MultipartBody body = new MultipartBody(attachments);
			
			WebClient client = WebClient.create(privyCoreUrl + privyConfirmOtp).headers(mapHeader);
			Response response = client.post(body);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				String jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy confirm OTP response: {}", jsonResponse);
				return gson.fromJson(jsonResponse, PrivyConfirmOtpResponse.class);
			}
			
		} catch (Exception e) {
			LOG.error("Privy confirm OTP exception: {}", e.getLocalizedMessage(), e);
			PrivyConfirmOtpResponse response = new PrivyConfirmOtpResponse();
			response.setCode(StatusCode.PRIVY_ERROR);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
	}

	@Override
	public PrivyRegisterStatusV2Response checkRegisterStatusV2(MsVendoroftenant vendoroftenant, String userToken, AuditContext audit) throws IOException {
		String username = vendoroftenant.getClientId();
		String password = vendoroftenant.getClientSecret();
		String merchantKey = vendoroftenant.getVendorMerchantKey();
		String authToken = HttpHeaders.buildBasicAuthorization(username, password);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, authToken);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORMDATA);
		mapHeader.add(HttpHeaders.PRIVY_MERCHANT_KEY, merchantKey);
		
		List<Attachment> attachments = new ArrayList<>();
		attachments.add(prepareStringAttachment("token", userToken));
		MultipartBody body = new MultipartBody(attachments);
		LOG.info("Check register Privy status for token: {}", userToken);
		
		WebClient client = WebClient.create(privyCoreUrl + registerStatusUrl).headers(mapHeader);
		Response response = client.post(body);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String jsonResponse = IOUtils.toString(isReader);
		LOG.info("Check register Privy status response: {}", jsonResponse);
		
		return gson.fromJson(jsonResponse, PrivyRegisterStatusV2Response.class);
	}

	@Override
	public String getCheckRegisterStatusMessage(PrivyRegisterStatusV2Response response, AuditContext audit) {
		if (null == response || null == response.getData()) {
			return StringUtils.EMPTY;
		}
		
		if ("waiting".equalsIgnoreCase(response.getData().getStatus())) {
			return response.getMessage();
		}
		
		if (CONST_VERIFIED.equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			return "Registrasi berhasil";
		}
		
		// Get verification result based on error code
		String rejectCode = response.getData().getReject().getCode();
		if (REJECT_CODE_PRVS002.equals(rejectCode)) {
			return "Verifikasi user gagal. Foto Diri tidak sesuai.";
		}
		
		if (REJECT_CODE_PRVN002.equals(rejectCode)
				|| REJECT_CODE_PRVN004.equals(rejectCode)
				|| REJECT_CODE_PRVN009.equals(rejectCode)
				|| REJECT_CODE_PRVD009.equals(rejectCode)) {
			
			return "Verifikasi user gagal. NIK tidak sesuai.";
		}
		
		if (REJECT_CODE_PRVN005.equals(rejectCode)) {
			return "Verifikasi user gagal. Nama Lengkap dan Tanggal Lahir tidak sesuai.";
		}
		
		if (REJECT_CODE_PRVD005.equals(rejectCode)) {
			return "Verifikasi user gagal. Foto Diri tidak sesuai.";
		}
		
		LOG.warn("Unhandled Privy reject code: {}", rejectCode);
		return "Verifikasi user gagal. " + response.getData().getReject().getReason();
	}

	@Override
	public RegisterVerificationStatusBean getCheckRegisterVerificationResults(PrivyRegisterStatusV2Response response, AuditContext audit) {
		if (null == response || null == response.getData() || "waiting".equalsIgnoreCase(response.getData().getStatus())) {
			return new RegisterVerificationStatusBean();
		}
		
		if (CONST_VERIFIED.equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setBirthDate(GlobalVal.CONST_TRUE);
			bean.setLiveness(GlobalVal.CONST_TRUE);
			bean.setName(GlobalVal.CONST_TRUE);
			bean.setNik(GlobalVal.NIK_REGISTERED);
			bean.setSelfieCheck(GlobalVal.CONST_TRUE);
			return bean;
		}
		
		// Get verification result based on error code
		String rejectCode = response.getData().getReject().getCode();
		if (REJECT_CODE_PRVS002.equals(rejectCode)) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setBirthDate(GlobalVal.CONST_TRUE);
			bean.setLiveness(GlobalVal.CONST_TRUE);
			bean.setName(GlobalVal.CONST_TRUE);
			bean.setNik(GlobalVal.NIK_REGISTERED);
			bean.setSelfieCheck(GlobalVal.CONST_FALSE);
			return bean;
		}
		
		if (REJECT_CODE_PRVN002.equals(rejectCode)
				|| REJECT_CODE_PRVN004.equals(rejectCode)
				|| REJECT_CODE_PRVN009.equals(rejectCode)
				|| REJECT_CODE_PRVD009.equals(rejectCode)) {
			
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setNik(GlobalVal.NIK_UNREGISTERED);
			return bean;
		}
		
		if (REJECT_CODE_PRVN005.equals(rejectCode)) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setBirthDate(GlobalVal.CONST_FALSE);
			bean.setName(GlobalVal.CONST_FALSE);
			bean.setNik(GlobalVal.NIK_REGISTERED);
			return bean;
		}
		
		if (REJECT_CODE_PRVD005.equals(rejectCode)) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setSelfieCheck(GlobalVal.CONST_FALSE);
			return bean;
		}
		
		LOG.warn("Unhandled Privy reject code: {}", rejectCode);
		return new RegisterVerificationStatusBean();
	}
	
	private String generateLivenessSignature(Date timestamp, String username, String password, String merchantKey, AuditContext audit) {
		try {
			return PrivyUtils.createLivenessSignature(timestamp, username, password, merchantKey, StringUtils.EMPTY);
		} catch (Exception e) {
			throw new PrivyException(getMessage("businesslogic.privy.general.failedtogeneratesignature", null, audit));
		}
		
	}

	@Override
	public PrivyLivenessUrlResponse getPrivyLivenessUrl(MsTenant tenant, AuditContext audit) throws IOException {
		
		Date timestamp = new Date();
		
		MsTenantSettings merchantKeySettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "PRIVY_LIVENESS_MERCHANT_KEY");
		MsTenantSettings usernameSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "PRIVY_LIVENESS_USERNAME");
		MsTenantSettings passwordSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "PRIVY_LIVENESS_PASSWORD");

		commonValidatorLogic.validateNotNull(merchantKeySettings, getMessage(LIVENESS_NOT_CONFIGURED_MSG, new Object[] { "Privy Liveness Merchant Key", tenant.getTenantCode() }, audit), StatusCode.TENANT_SETTING_NOT_CONFIGURED);
		commonValidatorLogic.validateNotNull(usernameSettings, getMessage(LIVENESS_NOT_CONFIGURED_MSG, new Object[] { "Privy Liveness Username", tenant.getTenantCode() }, audit), StatusCode.TENANT_SETTING_NOT_CONFIGURED);
		commonValidatorLogic.validateNotNull(passwordSettings, getMessage(LIVENESS_NOT_CONFIGURED_MSG, new Object[] { "Privy Liveness Password", tenant.getTenantCode() }, audit), StatusCode.TENANT_SETTING_NOT_CONFIGURED);

		String merchantKey = merchantKeySettings.getSettingValue();
		String username = usernameSettings.getSettingValue();
		String password = passwordSettings.getSettingValue();

		Map<String, String> mapHeader = new HashMap<>();
		mapHeader.put("Merchant-Key", merchantKey);
		mapHeader.put("Timestamp", MssTool.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssZ"));
		mapHeader.put("Signature", generateLivenessSignature(timestamp, username, password, merchantKey, audit));
		Headers headers = Headers.of(mapHeader);
		
		Request okHttpRequest = new Request.Builder()
				.url(livenessBaseUrl + livenessUrl)
				.headers(headers)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.readTimeout(30L, TimeUnit.SECONDS)
				.connectTimeout(30L, TimeUnit.SECONDS)
				.build();
		
	    long startTime = System.currentTimeMillis(); 

		okhttp3.Response okHttpResponse = client.newCall(okHttpRequest).execute();
	    long endTime = System.currentTimeMillis(); 
	    long duration = endTime - startTime; 

		String responseBody = okHttpResponse.body().string();
		LOG.info("Get Privy liveness URL duration {} ms", duration);
		LOG.info("Get Privy liveness URL for tenant {} response: {}", tenant.getTenantCode(), responseBody);
		return gson.fromJson(responseBody, PrivyLivenessUrlResponse.class);
	}

	@Override
	public PrivyLivenessUrlResponse getPrivyLivenessV3Url(MsTenant tenant, AuditContext audit) throws IOException {

		String applicationId = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_APPLICATION_ID, true, audit).getSettingValue();
		String clientId = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_CLIENT_ID, true, audit).getSettingValue();
		String clientSecret = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_CLIENT_SECRET, true, audit).getSettingValue();
		String salt = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_SALT, true, audit).getSettingValue();
		String publicKeyBase64 = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_PUBLIC_KEY, true, audit).getSettingValue();

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

		Date timestamp = new Date();
		String timestampString = sdf.format(timestamp);

		String publicKey = new String(Base64.getDecoder().decode(publicKeyBase64));
		String signature = generateLivenessV3Signature(salt, clientId, clientSecret, publicKey, timestampString);

		Map<String, String> mapHeader = new HashMap<>();
		mapHeader.put("X-P-Application-ID", applicationId);
		mapHeader.put("X-Channel-Code", "");
		mapHeader.put("X-P-Liveness-Timestamp", timestampString);
		mapHeader.put("X-P-Signature-Payload", signature);
		
		Headers headers = Headers.of(mapHeader);

		OkHttpClient client = new OkHttpClient.Builder()
			.connectTimeout(10L, TimeUnit.SECONDS)
			.readTimeout(10L, TimeUnit.SECONDS)
			.build();

		Request request = new Request.Builder()
			.url(livenessBaseUrl + livenessV3Url)
			.headers(headers)
			.get()
			.build();
		
		Date startTime = new Date();
		try (okhttp3.Response response = client.newCall(request).execute()) {
			logProcessDuration("Generate Privy liveness V3 URL", startTime, new Date());
			String jsonResponse = response.body().string();
			LOG.info("Generate Privy liveness V3 URL response code: {}, body: {}", response.code(), jsonResponse);

			if (response.code() != 200) {
				throw new PrivyException("Invalid Privy response code");
			}

			return gson.fromJson(jsonResponse, PrivyLivenessUrlResponse.class);
		}
	}

	private String generateLivenessV3Signature(String salt, String clientId, String clientSecret, String publicKey, String timestamp) {
		try {
			return PrivyUtils.createLivenessV3Signature(salt, clientId, clientSecret, publicKey, timestamp);
		} catch (Exception e) {
			throw new PrivyException("Failed to generate signature", e);
		}
	}

	@Override
	public PrivyRegisterV2ResponseContainer registerV2WithInvitationRequest(UserBean userData, String trxNo, PrivyVerifV2CredentialsBean credentials, AuditContext audit) {

		String jsonRequest = null;
		String jsonResponse = null;

		try {
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add("Merchant-Key", credentials.getMerchantKey());
			mapHeader.add(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBasicAuthorization(credentials.getUsername(), credentials.getPassword()));
			mapHeader.add(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);

			WebClient client = WebClient.create(verificationV2Url).headers(mapHeader);

			HTTPConduit conduit = WebClient.getConfig(client).getHttpConduit();
			conduit.getClient().setConnectionTimeout(10_000); // 10 seconds
			conduit.getClient().setReceiveTimeout(70_000); // 70 seconds

			// Prepare request body
			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			
			PrivyRegisterV2Request request = new PrivyRegisterV2Request();
			request.setTrId(TRX_ID_PREFIX + trxNo);
			request.setNik(userData.getIdNo());
			request.setName(userData.getUserName());
			request.setDob(MssTool.formatDateToStringIn(dob, GlobalVal.DATE_FORMAT_DASH_IN));
			request.setEmail(userData.getEmail());
			request.setPhone(MssTool.changePrefixToPlus62(userData.getUserPhone()));
			request.setSelfie("base64Selfie");
			request.setKtp("base64Ktp");

			String logRequest = gson.toJson(request);
			LOG.info("Privy V2 register request: {}", logRequest);

			request.setSelfie(MssTool.cutImageStringPrefix(userData.getSelfPhoto()));
			request.setKtp(MssTool.cutImageStringPrefix(userData.getIdPhoto()));
			jsonRequest = gson.toJson(request);

			Response response = client.post(jsonRequest);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy V2 register response code: {}, body: {}", response.getStatus(), jsonResponse);

				PrivyRegisterV2Response registerResponse = gson.fromJson(jsonResponse, PrivyRegisterV2Response.class);

				PrivyRegisterV2ResponseContainer responseContainer = new PrivyRegisterV2ResponseContainer();
				responseContainer.setJsonRequest(jsonRequest);
				responseContainer.setJsonResponse(jsonResponse);
				responseContainer.setResponse(registerResponse);
				return responseContainer;
			}

		} catch (Exception e) {

			PrivyRegisterV2Response registerResponse = new PrivyRegisterV2Response();
			registerResponse.setCode(StatusCode.UNKNOWN);
			registerResponse.setStatus("failed");
			registerResponse.setMessage(e.getLocalizedMessage());

			PrivyRegisterV2ResponseContainer responseContainer = new PrivyRegisterV2ResponseContainer();
			responseContainer.setJsonRequest(jsonRequest);
			responseContainer.setJsonResponse(jsonResponse);
			responseContainer.setResponse(registerResponse);
			return responseContainer;

		}

	}

	@Override
	public AmMsuser insertRegisteredUserV2(UserBean userData, PrivyRegisterV2Response response, MsTenant tenant, RegistrationType registrationType, MsLov lovUserType, AuditContext audit) {
		
		String hashedPhone = MssTool.getHashedString(userData.getUserPhone());
		byte[] phoneBytea = personalDataEncLogic.encryptFromString(userData.getUserPhone());

		String hashedIdNo = MssTool.getHashedString(userData.getIdNo());
		String emailService = (RegistrationType.INVITATION_SMS == registrationType) ? "1" :"0";
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(userData.getIdEmailHosting());

		AmMsrole role = null;
		if (null != lovUserType && GlobalVal.CODE_LOV_USER_TYPE_EMPLOYEE.equals(lovUserType.getCode())) {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_BM_MF, tenant.getTenantCode());
		} else {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		}

		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(userData.getIdNo());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = userData.getUserName().split(" ");
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setFullName(StringUtils.upperCase(userData.getUserName()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword("newInv");
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("1");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		} else {
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setHashedPhone(hashedPhone);
			user.setPassword("newInv");
			user.setIsActive("1");
			user.setIsDormant("0");
			user.setChangePwdLogin("1");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUserNewTran(user);
		}

		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_PRIVY_ID);
		Date activatedDate = new Date();
		Date expiredDate =  DateUtils.addDays(activatedDate, getPrivyCertActiveDurationInDays());

		if (null == vendorUser) {
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive("0");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_PRIVY_ID));
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(response.getPrivyId());
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		} else {
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive("0");
			vendorUser.setUsrUpd(audit.getCallerId());
			vendorUser.setDtmUpd(new Date());
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(response.getPrivyId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);
		}

		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto(), true);
			byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto(), true);
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(userData.getIdNo());
			personalDataBean.setPhoneRaw(userData.getUserPhone());
			personalDataBean.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);

		} else {

			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto());
			byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());

			AmUserPersonalData userPersonalData = personalData.getUserPersonalData();
			userPersonalData.setUsrUpd(audit.getCallerId());
			userPersonalData.setDtmUpd(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);

			personalData.setUserPersonalData(userPersonalData);
			personalData.setSelfPhotoRaw(selfiePhoto);
			personalData.setPhotoIdRaw(idPhoto);
			personalData.setIdNoRaw(userData.getIdNo());
			personalData.setPhoneRaw(userData.getUserPhone());
			personalData.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().updateUserPersonalDataNewTrans(personalData);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}

		return user;

	}

	private int getPrivyCertActiveDurationInDays() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_PRIVY_CERTIFICATE_EXPIRE_TIME);
		if (null == gs) {
			return PRIVY_CERT_ACTIVE_DURATION_DAYS;
		}

		try {
			return Integer.parseInt(gs.getGsValue());
		} catch (Exception e) {
			return PRIVY_CERT_ACTIVE_DURATION_DAYS;
		}
	}

	@Override
	public PrivyRegisterV2ResponseContainer registerV2WithExternalRequest(RegisterExternalRequest request, String trxNo, PrivyVerifV2CredentialsBean credentials, AuditContext audit) {
		
		String jsonRequest = null;
		String jsonResponse = null;

		try {
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add("Merchant-Key", credentials.getMerchantKey());
			mapHeader.add(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBasicAuthorization(credentials.getUsername(), credentials.getPassword()));
			mapHeader.add(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);

			WebClient client = WebClient.create(verificationV2Url).headers(mapHeader);

			HTTPConduit conduit = WebClient.getConfig(client).getHttpConduit();
			conduit.getClient().setConnectionTimeout(10_000); // 10 seconds
			conduit.getClient().setReceiveTimeout(70_000); // 70 seconds

			// Prepare request body
			Date dob = MssTool.formatStringToDate(request.getTglLahir(), GlobalVal.DATE_FORMAT);
			String trId = TRX_ID_PREFIX + trxNo;
			
			PrivyRegisterV2Request requestRegister = new PrivyRegisterV2Request();
			requestRegister.setTrId(trId);
			requestRegister.setNik(request.getIdKtp());
			requestRegister.setName(request.getNama());
			requestRegister.setDob(MssTool.formatDateToStringIn(dob, GlobalVal.DATE_FORMAT_DASH_IN));
			requestRegister.setEmail(request.getEmail());
			requestRegister.setPhone(MssTool.changePrefixToPlus62(request.getTlp()));
			requestRegister.setSelfie("base64Selfie");
			requestRegister.setKtp("base64Ktp");

			String logRequest = gson.toJson(requestRegister);
			LOG.info("Privy V2 register request: {}", logRequest);

			requestRegister.setSelfie(MssTool.cutImageStringPrefix(request.getSelfPhoto()));
			requestRegister.setKtp(MssTool.cutImageStringPrefix(request.getIdPhoto()));
			jsonRequest = gson.toJson(requestRegister);
			
			Response response = client.post(jsonRequest);

			try (InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity())) {
				jsonResponse = IOUtils.toString(isReader);
				LOG.info("Privy V2 register response code: {}, body: {}", response.getStatus(), jsonResponse);

				PrivyRegisterV2Response registerResponse = gson.fromJson(jsonResponse, PrivyRegisterV2Response.class);

				PrivyRegisterV2ResponseContainer responseContainer = new PrivyRegisterV2ResponseContainer();
				responseContainer.setJsonRequest(jsonRequest);
				responseContainer.setJsonResponse(jsonResponse);
				responseContainer.setResponse(registerResponse);
				return responseContainer;
			}

		} catch (Exception e) {

			PrivyRegisterV2Response registerResponse = new PrivyRegisterV2Response();
			registerResponse.setCode(StatusCode.UNKNOWN);
			registerResponse.setStatus("failed");
			registerResponse.setMessage(e.getLocalizedMessage());

			PrivyRegisterV2ResponseContainer responseContainer = new PrivyRegisterV2ResponseContainer();
			responseContainer.setJsonRequest(jsonRequest);
			responseContainer.setJsonResponse(jsonResponse);
			responseContainer.setResponse(registerResponse);
			return responseContainer;

		}
	}

	@Override
	public AmMsuser insertRegisteredUserV2External(RegisterExternalRequest request, PrivyRegisterV2Response response, MsTenant tenant, AuditContext audit) {
		
		userValidatorLogic.removeUnnecessaryRegisterExternalParam(request, tenant);
		boolean doesNotNeedPassword = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_ACTIVATION);

		String hashedPhone = MssTool.getHashedString(request.getTlp());
		byte[] phoneBytea = personalDataEncLogic.encryptFromString(request.getTlp());

		String hashedIdNo = MssTool.getHashedString(request.getIdKtp());
		String emailService = (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) ? "1" :"0";
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(request.getIdEmailHosting());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(request.getIdKtp());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = request.getNama().split(" ");
			String hashedPassword = null;
			if (doesNotNeedPassword) {
				hashedPassword = "noPassword";
			} else {
				hashedPassword = PasswordHash.createHash(request.getPassword());
			}
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setFullName(StringUtils.upperCase(request.getNama()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword(hashedPassword);
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("0");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		} else {
			if (StringUtils.isNotBlank(request.getPassword())) {
				String hashedPassword = PasswordHash.createHash(request.getPassword());
				user.setPassword(hashedPassword);
			}
			user.setIsActive("1");
			user.setIsDormant("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setHashedPhone(hashedPhone);
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUserNewTran(user);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_PRIVY_ID);
		
		Date activatedDate = new Date();
		Date expiredDate = DateUtils.addDays(activatedDate, getPrivyCertActiveDurationInDays());
		
		if (null == vendorUser) {
			
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_PRIVY_ID));
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setVendorRegistrationId(response.getPrivyId());
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		} else {
			
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive("1");
			vendorUser.setUsrUpd(audit.getCallerId());
			vendorUser.setDtmUpd(new Date());
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setVendorRegistrationId(response.getPrivyId());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUserNewTran(vendorUser);
		}
		
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(request.getTglLahir(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = StringUtils.isBlank(request.getSelfPhoto()) ? null : MssTool.imageStringToByteArray(request.getSelfPhoto());
			byte[] idPhoto = StringUtils.isBlank(request.getIdPhoto()) ? null : MssTool.imageStringToByteArray(request.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(request.getProvinsi());
			zipcodeCityBean.setKota(request.getKota());
			zipcodeCityBean.setKecamatan(request.getKecamatan());
			zipcodeCityBean.setKelurahan(request.getKelurahan());
			zipcodeCityBean.setZipcode(request.getKodePos());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(request.getJenisKelamin()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(request.getTmpLahir()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(request.getIdKtp());
			personalDataBean.setPhoneRaw(request.getTlp());
			personalDataBean.setAddressRaw(StringUtils.upperCase(request.getAlamat()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
		
		return user;
	}

	@Override
	public RegisterVerificationStatusBean buildVerificationStatusBean(PrivyRegisterV2Response response) {
		
		if (null == response) {
			return new RegisterVerificationStatusBean();
		}

		if (response.getCode() == 200 &&  CONST_VERIFIED.equalsIgnoreCase(response.getUserStatus())) {
			RegisterVerificationStatusBean statusBean = new RegisterVerificationStatusBean();
			statusBean.setLiveness(GlobalVal.CONST_TRUE);
			return statusBean;
		}

		return new RegisterVerificationStatusBean();
	}

}