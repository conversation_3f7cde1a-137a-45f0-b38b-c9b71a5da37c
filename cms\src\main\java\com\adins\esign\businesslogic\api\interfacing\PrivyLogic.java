package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.privy.PrivyRegisterV2ResponseContainer;
import com.adins.esign.model.custom.privy.PrivyUploadDocRequest;
import com.adins.esign.model.custom.privy.PrivyUploadDocResponse;
import com.adins.esign.model.custom.privy.PrivyVerifV2CredentialsBean;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.privy.PrivyConfirmOtpRequest;
import com.adins.esign.webservices.model.privy.PrivyConfirmOtpResponse;
import com.adins.esign.webservices.model.privy.PrivyLivenessUrlResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterRequest;
import com.adins.esign.webservices.model.privy.PrivyRegisterResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterStatusResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterStatusV2Response;
import com.adins.esign.webservices.model.privy.PrivyRegisterV2Response;
import com.adins.esign.webservices.model.privy.PrivyRequestOtpRequest;
import com.adins.esign.webservices.model.privy.PrivyRequestOtpResponse;
import com.adins.esign.webservices.model.privy.PrivyUserAccessTokenRequest;
import com.adins.esign.webservices.model.privy.PrivyUserAccessTokenResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface PrivyLogic {
	
	// Registration
	PrivyRegisterResponse register(PrivyRegisterRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	AmMsuser insertRegisteredUser(RegisterExternalRequest request, String privyId, MsTenant tenant, MsVendor vendor, AuditContext audit);
	String buildRegisterErrorMessage(PrivyRegisterResponse response, AuditContext audit);
	PrivyRegisterStatusResponse checkRegisterStatus(MsTenant tenant, MsVendor vendor, String requestId, AuditContext audit);
	PrivyRegisterStatusV2Response checkRegisterStatusV2(MsVendoroftenant vendoroftenant, String userToken, AuditContext audit) throws IOException;
	String getCheckRegisterStatusMessage(PrivyRegisterStatusV2Response response, AuditContext audit);
	RegisterVerificationStatusBean getCheckRegisterVerificationResults(PrivyRegisterStatusV2Response response, AuditContext audit);

	// Registration / Verif V2
	PrivyRegisterV2ResponseContainer registerV2WithInvitationRequest(UserBean userData, String trxNo, PrivyVerifV2CredentialsBean credentials, AuditContext audit);
	PrivyRegisterV2ResponseContainer registerV2WithExternalRequest(RegisterExternalRequest request, String trxNo, PrivyVerifV2CredentialsBean credentials, AuditContext audit);
	AmMsuser insertRegisteredUserV2(UserBean userData, PrivyRegisterV2Response response, MsTenant tenant, RegistrationType registrationType, MsLov lovUserType, AuditContext audit);
	AmMsuser insertRegisteredUserV2External(RegisterExternalRequest request, PrivyRegisterV2Response response, MsTenant tenant, AuditContext audit);
	RegisterVerificationStatusBean buildVerificationStatusBean(PrivyRegisterV2Response response);

	// OTP
	PrivyUserAccessTokenResponse userAccessToken(PrivyUserAccessTokenRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	PrivyRequestOtpResponse requestOtp(PrivyRequestOtpRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	String buildUserAccessTokenErrorMessage(PrivyUserAccessTokenResponse response, AuditContext audit);
	PrivyConfirmOtpResponse confirmOtp(PrivyConfirmOtpRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	
	PrivyUploadDocResponse uploadDoc(PrivyUploadDocRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException;

	// Liveness
	PrivyLivenessUrlResponse getPrivyLivenessUrl(MsTenant tenant, AuditContext audit) throws IOException;
	PrivyLivenessUrlResponse getPrivyLivenessV3Url(MsTenant tenant, AuditContext audit) throws IOException;
}
