package com.adins.esign.webservices.model.privy;

import com.google.gson.annotations.SerializedName;

public class PrivyRegisterV2Response {

    private Integer code;
    private String status;
    private String userStatus;
    private Long balance;
    @SerializedName("transaction_id") private String transactionId;
    private String message;
    @SerializedName("rejected_code") private String rejectedCode;
    @SerializedName("privy_id") private String privyId;

    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserStatus() {
        return this.userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public Long getBalance() {
        return this.balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public String getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRejectedCode() {
        return this.rejectedCode;
    }

    public void setRejectedCode(String rejectedCode) {
        this.rejectedCode = rejectedCode;
    }

    public String getPrivyId() {
        return this.privyId;
    }

    public void setPrivyId(String privyId) {
        this.privyId = privyId;
    }

}
